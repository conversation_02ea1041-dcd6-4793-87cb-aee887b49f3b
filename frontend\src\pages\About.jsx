import React, { useEffect } from 'react';
import { Mail, Users, Zap, Shield, Heart, Star, Award, Lightbulb, Clock, AlertCircle, DollarSign, Home, Info, Sparkles, Palette, Eye, Download, Rocket, Globe, Target, TrendingUp } from 'lucide-react';
import SEO from '../components/SEO';
import Logo from '../components/Logo';
import StyledSocialIcons from '../components/StyledSocialIcons';
import Breadcrumbs, { BreadcrumbStructuredData } from '../components/Breadcrumbs';
import { useNavigate } from 'react-router-dom';

const About = () => {
  const navigate = useNavigate();

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  // Create breadcrumbs for about page
  const aboutBreadcrumbs = [
    { label: 'Home', href: '/', icon: Home },
    { label: 'About Us', href: null, icon: Info }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 py-8 relative overflow-hidden">
      {/* SEO */}
      <SEO
        title="About Gen Free AI - Free AI Image Generator | Our Story & Mission"
        description="Learn about Gen Free AI, our mission to democratize AI image generation, and how we're making creative tools accessible to everyone. Contact <NAME_EMAIL>."
        keywords="about gen free ai, ai image generator, free tools, mission, contact, <EMAIL> "
      />

      {/* Structured Data for Breadcrumbs */}
      <BreadcrumbStructuredData breadcrumbs={aboutBreadcrumbs} />

      {/* Background Elements */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden -z-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-purple-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '0s' }}></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-pink-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-40 left-20 w-20 h-20 bg-orange-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '4s' }}></div>
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-blue-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '6s' }}></div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Breadcrumbs */}
        <Breadcrumbs customBreadcrumbs={aboutBreadcrumbs} />

        {/* Enhanced Header */}
        <div className="text-center mb-16">
          <div className="relative inline-block mb-8 animate-bounce-in">
            <Logo size="2xl" />
            <div className="absolute -top-2 -right-2 animate-bounce">
              <Sparkles className="w-8 h-8 text-yellow-500" />
            </div>
          </div>

          <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-orange-500 bg-clip-text text-transparent animate-gradient-x mb-6">
            About Gen Free AI
          </h1>

          <p className="text-xl sm:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
            Democratizing AI-powered creativity for everyone, everywhere
          </p>

          {/* Feature Pills */}
          <div className="flex flex-wrap justify-center gap-4">
            <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900 dark:to-pink-900 rounded-full shadow-lg">
              <Rocket className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              <span className="font-semibold text-purple-700 dark:text-purple-300">Innovation First</span>
            </div>
            <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900 dark:to-cyan-900 rounded-full shadow-lg">
              <Globe className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              <span className="font-semibold text-blue-700 dark:text-blue-300">Global Community</span>
            </div>
            <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900 dark:to-emerald-900 rounded-full shadow-lg">
              <Heart className="w-5 h-5 text-green-600 dark:text-green-400" />
              <span className="font-semibold text-green-700 dark:text-green-300">Always Free</span>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="space-y-12">
          {/* Enhanced Mission Section */}
          <section className="bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-3xl shadow-2xl p-8 sm:p-12 animate-fade-in-up border border-gray-600">
            <div className="flex items-center gap-4 mb-8">
              <div className="p-3 bg-gradient-to-r from-red-500 to-pink-500 rounded-2xl shadow-lg">
                <Heart className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-red-500 to-pink-500 bg-clip-text text-transparent">
                Our Mission
              </h2>
            </div>

            <div className="text-gray-300 space-y-6 text-lg leading-relaxed">
              <p className="text-xl font-medium text-gray-200">
                At Gen Free AI, we believe that creativity should be accessible to everyone. Our mission is to democratize
                AI-powered image generation, making it free and easy for anyone to bring their imagination to life.
              </p>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="p-6 bg-gradient-to-br from-purple-900/20 to-pink-900/20 rounded-2xl border border-purple-800">
                  <Target className="w-8 h-8 text-purple-400 mb-3" />
                  <h3 className="font-semibold text-white mb-2">For Everyone</h3>
                  <p className="text-sm">
                    Whether you're an artist, designer, content creator, or just someone with a creative spark, our platform
                    empowers you to generate stunning, high-quality images from simple text descriptions.
                  </p>
                </div>

                <div className="p-6 bg-gradient-to-br from-blue-900/20 to-cyan-900/20 rounded-2xl border border-blue-800">
                  <Shield className="w-8 h-8 text-blue-400 mb-3" />
                  <h3 className="font-semibold text-white mb-2">Privacy First</h3>
                  <p className="text-sm">
                    To protect your privacy and keep our service free, all generated images are automatically deleted after 6 hours.
                    Make sure to download your creations promptly!
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-orange-900/20 to-red-900/20 rounded-2xl p-6 border border-orange-800">
                <div className="flex items-center gap-3 mb-3">
                  <Sparkles className="w-6 h-6 text-orange-400" />
                  <h3 className="font-semibold text-white">Completely Free Forever</h3>
                </div>
                <p className="text-gray-300">
                  No hidden costs, no subscriptions, no limits. We're committed to keeping AI creativity accessible to everyone,
                  supported by carefully selected advertisements that help us maintain our servers and continue improving.
                </p>
              </div>
            </div>
          </section>

          {/* Enhanced Features Grid */}
          <section className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="group bg-gradient-to-br from-yellow-900/20 to-orange-900/20 rounded-2xl shadow-lg hover:shadow-2xl p-6 animate-fade-in-up border border-yellow-800 transition-all duration-300 hover:scale-105">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-3 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Zap className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white">
                  Lightning Fast
                </h3>
              </div>
              <p className="text-gray-300 leading-relaxed">
                Generate high-quality images in seconds with our optimized AI models and infrastructure.
              </p>
            </div>

            <div className="group bg-gradient-to-br from-green-900/20 to-emerald-900/20 rounded-2xl shadow-lg hover:shadow-2xl p-6 animate-fade-in-up border border-green-800 transition-all duration-300 hover:scale-105">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Shield className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white">
                  Privacy First
                </h3>
              </div>
              <p className="text-gray-300 leading-relaxed">
                Your creations are yours. We respect your privacy and automatically delete images after 6 hours to protect your data.
              </p>
            </div>

            <div className="group bg-gradient-to-br from-purple-900/20 to-pink-900/20 rounded-2xl shadow-lg hover:shadow-2xl p-6 animate-fade-in-up border border-purple-800 transition-all duration-300 hover:scale-105">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Star className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white">
                  High Quality
                </h3>
              </div>
              <p className="text-gray-300 leading-relaxed">
                Professional-grade images perfect for any project, from social media to presentations.
              </p>
            </div>

            <div className="group bg-gradient-to-br from-blue-900/20 to-cyan-900/20 rounded-2xl shadow-lg hover:shadow-2xl p-6 animate-fade-in-up border border-blue-800 transition-all duration-300 hover:scale-105">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-3 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Users className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white">
                  Community Driven
                </h3>
              </div>
              <p className="text-gray-300 leading-relaxed">
                Built for creators, by creators. We listen to our community and continuously improve based on feedback.
              </p>
            </div>
          </section>

          {/* Enhanced How We Keep It Free Section */}
          <section className="bg-gradient-to-br from-green-900/20 via-emerald-800/10 to-green-900/20 border-2 border-green-800 rounded-3xl p-8 sm:p-12 animate-fade-in-up shadow-2xl">
            <div className="flex items-center gap-4 mb-8">
              <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl shadow-lg">
                <DollarSign className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                How We Keep It Free
              </h2>
            </div>

            <div className="text-gray-300 space-y-6 text-lg leading-relaxed">
              <p className="text-xl font-medium text-gray-200">
                Gen Free AI is completely free to use, and we're committed to keeping it that way. To maintain our servers,
                cover operational costs, and continue improving our service, we display third-party advertisements on our website.
              </p>

              <div className="bg-gray-800 rounded-2xl p-6 border border-green-700">
                <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
                  <TrendingUp className="w-6 h-6 text-green-400" />
                  Advertisement Revenue Helps Us:
                </h3>
                <div className="grid sm:grid-cols-2 gap-4">
                  <div className="flex items-center gap-3 p-3 bg-green-900/20 rounded-lg">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Keep AI image generation free for everyone</span>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-green-900/20 rounded-lg">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Maintain and upgrade server infrastructure</span>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-green-900/20 rounded-lg">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Develop new features and improvements</span>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-green-900/20 rounded-lg">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Ensure reliable service availability</span>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-blue-900/20 to-cyan-900/20 rounded-2xl p-6 border border-blue-800">
                <div className="flex items-center gap-3 mb-3">
                  <Shield className="w-6 h-6 text-blue-400" />
                  <h3 className="font-semibold text-white">Our Commitment</h3>
                </div>
                <p className="text-gray-300">
                  We carefully select our advertising partners to ensure ads are relevant and non-intrusive.
                  Your support through ad engagement helps us continue providing this free service to the community.
                </p>
              </div>
            </div>
          </section>

          {/* Enhanced Technology Section */}
          <section className="bg-gradient-to-br from-blue-900/20 via-purple-800/10 to-blue-900/20 border-2 border-blue-800 rounded-3xl p-8 sm:p-12 animate-fade-in-up shadow-2xl">
            <div className="flex items-center gap-4 mb-8">
              <div className="p-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl shadow-lg">
                <Lightbulb className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                Cutting-Edge Technology
              </h2>
            </div>

            <div className="text-gray-300 space-y-6 text-lg leading-relaxed">
              <p className="text-xl font-medium text-gray-200">
                Gen Free AI leverages state-of-the-art artificial intelligence models to transform your text descriptions
                into beautiful, unique images. Our platform combines advanced machine learning with an intuitive interface
                to make AI art creation accessible to everyone.
              </p>

              <div className="grid md:grid-cols-3 gap-6">
                <div className="group bg-gradient-to-br from-gray-800 to-orange-900/20 rounded-2xl p-6 border border-orange-700 shadow-lg hover:shadow-2xl transition-all duration-300 hover:scale-105">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="p-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg group-hover:scale-110 transition-transform duration-300">
                      <Award className="w-6 h-6 text-white" />
                    </div>
                    <h4 className="font-bold text-white">Advanced AI</h4>
                  </div>
                  <p className="text-sm text-gray-400">
                    Latest diffusion models optimized for speed and quality
                  </p>
                </div>

                <div className="group bg-gradient-to-br from-gray-800 to-yellow-900/20 rounded-2xl p-6 border border-yellow-700 shadow-lg hover:shadow-2xl transition-all duration-300 hover:scale-105">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg group-hover:scale-110 transition-transform duration-300">
                      <Zap className="w-6 h-6 text-white" />
                    </div>
                    <h4 className="font-bold text-white">Fast Processing</h4>
                  </div>
                  <p className="text-sm text-gray-400">
                    Optimized cloud infrastructure for lightning-fast generation
                  </p>
                </div>

                <div className="group bg-gradient-to-br from-gray-800 to-red-900/20 rounded-2xl p-6 border border-red-700 shadow-lg hover:shadow-2xl transition-all duration-300 hover:scale-105">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="p-2 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg group-hover:scale-110 transition-transform duration-300">
                      <Heart className="w-6 h-6 text-white" />
                    </div>
                    <h4 className="font-bold text-white">User Focused</h4>
                  </div>
                  <p className="text-sm text-gray-400">
                    Simple, intuitive interface designed for everyone
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* Image Expiration Policy Section */}
          {/* <section className="bg-amber-900/20 border border-amber-800 rounded-xl p-8 animate-fade-in-up">
            <div className="flex items-center gap-3 mb-6">
              <Clock className="w-8 h-8 text-amber-400" />
              <h2 className="text-3xl font-bold text-white">
                Image Storage Policy
              </h2>
            </div>
            <div className="space-y-4">
              <div className="flex items-start gap-3 p-4 bg-gray-800 rounded-lg border border-amber-700">
                <AlertCircle className="w-6 h-6 text-amber-400 mt-0.5 flex-shrink-0" />
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">
                    24-Hour Automatic Deletion
                  </h3>
                  <p className="text-gray-300 leading-relaxed">
                    <strong>Important:</strong> All generated images are automatically deleted from our servers after 24 hours.
                    This policy ensures your privacy and helps us maintain optimal server performance while keeping the service free for everyone.
                  </p>
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-4">
                <div className="p-4 bg-gray-800 rounded-lg">
                  <h4 className="font-semibold text-white mb-2 flex items-center gap-2">
                    <Shield className="w-4 h-4 text-green-500" />
                    Why 24 Hours?
                  </h4>
                  <ul className="text-sm text-gray-300 space-y-1">
                    <li>• Protects your privacy and data</li>
                    <li>• Reduces server storage costs</li>
                    <li>• Keeps the service free for everyone</li>
                    <li>• Complies with data protection standards</li>
                  </ul>
                </div>

                <div className="p-4 bg-gray-800 rounded-lg">
                  <h4 className="font-semibold text-white mb-2 flex items-center gap-2">
                    <Lightbulb className="w-4 h-4 text-yellow-500" />
                    Save Your Images
                  </h4>
                  <ul className="text-sm text-gray-300 space-y-1">
                    <li>• Download immediately after generation</li>
                    <li>• Use the History page to bulk download</li>
                    <li>• Right-click to save images directly</li>
                    <li>• Check expiration warnings in History</li>
                  </ul>
                </div>
              </div>

              <div className="bg-blue-900/20 border border-blue-800 rounded-lg p-4">
                <p className="text-blue-200 text-sm">
                  <strong>💡 Pro Tip:</strong> Visit your <a href="/history" className="underline hover:text-blue-300">History page</a> to see
                  expiration warnings and download multiple images before they're automatically deleted.
                </p>
              </div>
            </div>
          </section> */}

          {/* Contact Section */}
          <section className="bg-gray-800 rounded-xl shadow-sm p-8 animate-fade-in-up">
            <div className="flex items-center gap-3 mb-6">
              <Mail className="w-8 h-8 text-blue-500" />
              <h2 className="text-3xl font-bold text-white">
                Get in Touch
              </h2>
            </div>
            <div className="text-gray-300 space-y-4">
              <p className="text-lg">
                Have questions, suggestions, or just want to say hello? We'd love to hear from you!
              </p>
              <div className="bg-blue-900/20 rounded-lg p-6 border border-blue-800">
                <div className="flex items-center gap-3 mb-3">
                  <Mail className="w-5 h-5 text-blue-400" />
                  <h3 className="text-lg font-semibold text-blue-100">
                    Contact Email
                  </h3>
                </div>
                <a
                  href="mailto:<EMAIL>"
                  className="text-xl font-mono text-blue-400 hover:text-blue-300 transition-colors"
                >
                  <EMAIL>
                </a>
                <p className="text-sm text-blue-300 mt-2">
                  We typically respond within 24 hours
                </p>
              </div>
              <div className="grid md:grid-cols-2 gap-4 mt-6">
                <div className="p-4 bg-gray-700 rounded-lg">
                  <h4 className="font-semibold text-white mb-2">💡 Feature Requests</h4>
                  <p className="text-sm text-gray-300">
                    Have an idea for a new feature? We'd love to hear it!
                  </p>
                </div>
                <div className="p-4 bg-gray-700 rounded-lg">
                  <h4 className="font-semibold text-white mb-2">🐛 Bug Reports</h4>
                  <p className="text-sm text-gray-300">
                    Found an issue? Help us improve by reporting it.
                  </p>
                </div>
                <div className="p-4 bg-gray-700 rounded-lg">
                  <h4 className="font-semibold text-white mb-2">🤝 Partnerships</h4>
                  <p className="text-sm text-gray-300">
                    Interested in collaborating? Let's discuss opportunities.
                  </p>
                </div>
                <div className="p-4 bg-gray-700 rounded-lg">
                  <h4 className="font-semibold text-white mb-2">❓ General Support</h4>
                  <p className="text-sm text-gray-300">
                    Need help using the platform? We're here to assist.
                  </p>
                </div>
              </div>

              {/* Social Media Links */}
              <div className="mt-8 pt-6 border-t border-gray-600">
                <h3 className="text-lg font-semibold text-white mb-6 text-center">
                  Follow Us on Social Media
                </h3>
                <StyledSocialIcons />
                <p className="text-center text-sm text-gray-400 mt-6">
                  Stay updated with the latest features, tips, and AI art inspiration!
                </p>
              </div>
            </div>
          </section>

          {/* Enhanced Stats Section */}
          <section className="bg-gradient-to-br from-purple-900/20 via-pink-800/10 to-purple-900/20 border-2 border-purple-800 rounded-3xl p-8 sm:p-12 animate-fade-in-up shadow-2xl">
            <div className="text-center mb-12">
              <h2 className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                Join Our Growing Community
              </h2>
              <p className="text-xl text-gray-300 max-w-2xl mx-auto">
                Be part of the AI revolution that's making creativity accessible to everyone
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              <div className="group text-center p-6 bg-gradient-to-br from-gray-800 to-purple-900/20 rounded-2xl border border-purple-700 shadow-lg hover:shadow-2xl transition-all duration-300 hover:scale-105">
                <div className="flex items-center justify-center mb-4">
                  <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <Palette className="w-8 h-8 text-white" />
                  </div>
                </div>
                <div className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">2K+</div>
                <div className="text-gray-300 font-medium">Images Generated</div>
              </div>

              <div className="group text-center p-6 bg-gradient-to-br from-gray-800 to-blue-900/20 rounded-2xl border border-blue-700 shadow-lg hover:shadow-2xl transition-all duration-300 hover:scale-105">
                <div className="flex items-center justify-center mb-4">
                  <div className="p-3 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <Users className="w-8 h-8 text-white" />
                  </div>
                </div>
                <div className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent mb-2">1K+</div>
                <div className="text-gray-300 font-medium">Happy Creators</div>
              </div>

              <div className="group text-center p-6 bg-gradient-to-br from-gray-800 to-green-900/20 rounded-2xl border border-green-700 shadow-lg hover:shadow-2xl transition-all duration-300 hover:scale-105">
                <div className="flex items-center justify-center mb-4">
                  <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <Heart className="w-8 h-8 text-white" />
                  </div>
                </div>
                <div className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent mb-2">100%</div>
                <div className="text-gray-300 font-medium">Free Forever</div>
              </div>
            </div>

            <div className="mt-12 text-center">
              <div onClick={() => navigate('/generate')} className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-medium rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer" >
                <Sparkles className="w-5 h-5" />
                <span className="text-lg">Start Creating Today</span>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};

export default About;
