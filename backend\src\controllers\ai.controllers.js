const aiservice = require("../services/ai.service");
const Image = require("../../models/Image");

const getResult = async (req, res) => {
try {
    const { prompt, aspectRatio } = req.body;
    const imageUrl = await aiservice(prompt, aspectRatio);

    // Save image to MongoDB if generation was successful
    if (imageUrl) {
        // Save to database
        try {
            const newImage = new Image({
                prompt: prompt,
                imageUrl: imageUrl,
                aspectRatio: aspectRatio,
                userAgent: req.get('User-Agent') || '',
                ipAddress: req.ip || req.connection.remoteAddress || ''
            });

            await newImage.save();
        } catch (dbError) {
            console.error('❌ Failed to save image to database:', dbError.message);
            // Don't fail the request if DB save fails
        }
    }

    // Return the result in the expected format
    return res.json({ imageUrl: imageUrl });
} catch (error) {
    console.error('❌ Error in getResult:', error);
    return res.status(500).json({ error: 'Failed to generate image' });
}
};

module.exports = {
    getResult,
};

