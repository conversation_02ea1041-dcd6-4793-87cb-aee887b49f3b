import axios from 'axios';

const API_BASE_URL = `${import.meta.env.VITE_API_BASE_URL}/api`;

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 120000, // 2 minutes timeout for image generation
});

// Generate image from text prompt
export const generateImage = async (prompt, aspectRatio = '1:1') => {
  try {
    const response = await api.post('/ai/getResult', {
      prompt: prompt.trim(),
      aspectRatio: aspectRatio,
    });

    return {
      success: true,
      imageUrl: response.data.imageUrl,
      prompt: prompt,
    };
  } catch (error) {
    let errorMessage = 'Failed to generate image. Please try again.';

    if (error.response) {
      // Server responded with error status
      errorMessage = error.response.data?.message || `Server error: ${error.response.status}`;
    } else if (error.request) {
      // Request was made but no response received
      errorMessage = 'No response from server. Please check if the backend is running.';
    } else {
      // Something else happened
      errorMessage = error.message || 'An unexpected error occurred.';
    }

    return {
      success: false,
      error: errorMessage,
    };
  }
};

// Health check for backend
export const checkBackendHealth = async () => {
  try {
    const response = await api.get('/health');
    return response.status === 200;
  } catch (error) {
    return false;
  }
};

export default api;
