// Collection of random prompts for inspiration
export const randomPrompts = [
  "A majestic dragon soaring through clouds at sunset",
  "A cyberpunk cityscape with neon lights and flying cars",
  "A peaceful forest with glowing mushrooms and fairy lights",
  "A steampunk airship floating above Victorian London",
  "A magical underwater city with coral towers and mermaids",
  "A futuristic space station orbiting a distant planet",
  "A cozy cottage in a snowy mountain landscape",
  "A vibrant marketplace in an ancient Middle Eastern city",
  "A robot gardener tending to a beautiful flower garden",
  "A mystical portal opening in an enchanted forest",
  "A vintage train crossing a bridge over a misty valley",
  "A floating island with waterfalls cascading into clouds",
  "A neon-lit diner on a rainy night in the 1950s",
  "A phoenix rising from flames against a starry sky",
  "A crystal cave with glowing gems and underground lake",
  "A samurai warrior standing in a field of cherry blossoms",
  "A lighthouse on a rocky cliff during a thunderstorm",
  "A wizard's tower filled with floating books and potions",
  "A retro-futuristic car racing through a desert landscape",
  "A peaceful zen garden with a stone bridge and koi pond",
  "A pirate ship sailing through a sea of stars",
  "A medieval castle perched on a mountain peak",
  "A bioluminescent jungle with glowing plants and creatures",
  "A clockwork city with gears and steam-powered machines",
  "A serene lake reflecting the aurora borealis",
  "A post-apocalyptic wasteland with overgrown ruins",
  "A fairy tale cottage made of candy and sweets",
  "A space explorer discovering an alien artifact",
  "A traditional Japanese temple in autumn colors",
  "A surreal landscape with floating geometric shapes",
  "A vintage library with towering bookshelves and ladders",
  "A mechanical owl perched on a brass telescope",
  "A tropical paradise with crystal clear waters",
  "A gothic cathedral with stained glass windows",
  "A futuristic laboratory with holographic displays",
  "A whimsical tea party in a mushroom forest",
  "A desert oasis with palm trees and ancient ruins",
  "A steampunk submarine exploring the ocean depths",
  "A magical academy floating in the clouds",
  "A cybernetic tiger prowling through a digital jungle"
];

// Get a random prompt from the collection
export const getRandomPrompt = () => {
  const randomIndex = Math.floor(Math.random() * randomPrompts.length);
  return randomPrompts[randomIndex];
};

// Aspect ratio options
export const aspectRatios = [
  { label: "Square (1:1)", value: "1:1" },
  { label: "Portrait (3:4)", value: "3:4" },
  { label: "Landscape (4:3)", value: "4:3" },
  { label: "Vertical (9:16)", value: "9:16" },
  { label: "Horizontal (16:9)", value: "16:9" },
  { label: "Tall Portrait (2:3)", value: "2:3" },
  { label: "Wide Landscape (3:2)", value: "3:2" },
];

export default {
  randomPrompts,
  getRandomPrompt,
  aspectRatios,
};
