// History management for generated images
const HISTORY_KEY = 'ai_image_history';

// Get all history from localStorage
export const getImageHistory = () => {
  try {
    const history = localStorage.getItem(HISTORY_KEY);
    return history ? JSON.parse(history) : [];
  } catch (error) {
    return [];
  }
};

// Save a new image to history
export const saveImageToHistory = (imageData) => {
  try {
    const history = getImageHistory();
    const newEntry = {
      id: Date.now().toString(),
      imageUrl: imageData.imageUrl,
      prompt: imageData.prompt,
      aspectRatio: imageData.aspectRatio || '1:1',
      createdAt: new Date().toISOString(),
      timestamp: Date.now(),
    };

    // Add to beginning of array (most recent first)
    const updatedHistory = [newEntry, ...history];

    // Keep only last 50 images to prevent localStorage from getting too large
    const limitedHistory = updatedHistory.slice(0, 50);

    localStorage.setItem(HISTORY_KEY, JSON.stringify(limitedHistory));
    return newEntry;
  } catch (error) {
    return null;
  }
};

// Delete an image from history
export const deleteImageFromHistory = (imageId) => {
  try {
    const history = getImageHistory();
    const updatedHistory = history.filter(item => item.id !== imageId);
    localStorage.setItem(HISTORY_KEY, JSON.stringify(updatedHistory));
    return true;
  } catch (error) {
    console.error('Error deleting from history:', error);
    return false;
  }
};

// Clear all history
export const clearImageHistory = () => {
  try {
    localStorage.removeItem(HISTORY_KEY);
    return true;
  } catch (error) {
    console.error('Error clearing history:', error);
    return false;
  }
};

// Get history statistics
export const getHistoryStats = () => {
  const history = getImageHistory();
  return {
    totalImages: history.length,
    oldestImage: history.length > 0 ? history[history.length - 1].createdAt : null,
    newestImage: history.length > 0 ? history[0].createdAt : null,
  };
};

// Search history by prompt
export const searchHistory = (searchTerm) => {
  const history = getImageHistory();
  if (!searchTerm.trim()) return history;

  return history.filter(item =>
    item.prompt.toLowerCase().includes(searchTerm.toLowerCase())
  );
};

// Format date for display
export const formatDate = (dateString) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInHours = (now - date) / (1000 * 60 * 60);

  if (diffInHours < 1) {
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));
    return `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`;
  } else if (diffInHours < 24) {
    const hours = Math.floor(diffInHours);
    return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
  } else if (diffInHours < 24 * 7) {
    const days = Math.floor(diffInHours / 24);
    return `${days} day${days !== 1 ? 's' : ''} ago`;
  } else {
    return date.toLocaleDateString();
  }
};

// Image expiration settings (in hours)
const EXPIRATION_HOURS = 6; // Images expire after 6 hours
const WARNING_HOURS = 2; // Show warning when 2 hours or less remaining

// Check if an image is expired
export const isImageExpired = (createdAt) => {
  const now = new Date();
  const imageDate = new Date(createdAt);
  const diffInHours = (now - imageDate) / (1000 * 60 * 60);
  return diffInHours >= EXPIRATION_HOURS;
};

// Check if an image is close to expiring (within warning period)
export const isImageExpiring = (createdAt) => {
  const now = new Date();
  const imageDate = new Date(createdAt);
  const diffInHours = (now - imageDate) / (1000 * 60 * 60);
  const remainingHours = EXPIRATION_HOURS - diffInHours;
  return remainingHours > 0 && remainingHours <= WARNING_HOURS;
};

// Get time remaining until expiration
export const getTimeUntilExpiration = (createdAt) => {
  const now = new Date();
  const imageDate = new Date(createdAt);
  const diffInHours = (now - imageDate) / (1000 * 60 * 60);
  const remainingHours = EXPIRATION_HOURS - diffInHours;

  if (remainingHours <= 0) return 'Expired';

  if (remainingHours < 1) {
    const remainingMinutes = Math.floor(remainingHours * 60);
    return `${remainingMinutes} min${remainingMinutes !== 1 ? 's' : ''}`;
  }

  const hours = Math.floor(remainingHours);
  const minutes = Math.floor((remainingHours - hours) * 60);

  if (minutes === 0) {
    return `${hours} hour${hours !== 1 ? 's' : ''}`;
  }

  return `${hours}h ${minutes}m`;
};

// Get images that are expiring soon
export const getExpiringImages = () => {
  const history = getImageHistory();
  return history.filter(item => isImageExpiring(item.createdAt) && !isImageExpired(item.createdAt));
};

// Get count of expiring images
export const getExpiringImagesCount = () => {
  return getExpiringImages().length;
};
