import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx'

// Initialize dark mode as default
const initializeDarkMode = () => {
  const root = document.documentElement;
  root.classList.add('dark');
  root.classList.remove('light');
  // Always save dark theme to localStorage
  localStorage.setItem('theme', 'dark');
};

// Initialize dark mode before rendering
initializeDarkMode();

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
