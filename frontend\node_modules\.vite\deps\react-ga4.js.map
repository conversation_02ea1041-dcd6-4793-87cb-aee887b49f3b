{"version": 3, "sources": ["../../react-ga4/dist/gtag.js", "../../react-ga4/dist/format.js", "../../react-ga4/dist/ga4.js", "../../react-ga4/dist/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar gtag = function gtag() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  if (typeof window !== \"undefined\") {\n    var _window;\n    if (typeof window.gtag === \"undefined\") {\n      window.dataLayer = window.dataLayer || [];\n      window.gtag = function gtag() {\n        window.dataLayer.push(arguments);\n      };\n    }\n    (_window = window).gtag.apply(_window, args);\n  }\n};\nvar _default = gtag;\nexports[\"default\"] = _default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = format;\nvar smallWords = /^(a|an|and|as|at|but|by|en|for|if|in|nor|of|on|or|per|the|to|vs?\\.?|via)$/i;\nfunction toTitleCase(string) {\n  return string.toString().trim().replace(/[A-Za-z0-9\\u00C0-\\u00FF]+[^\\s-]*/g, function (match, index, title) {\n    if (index > 0 && index + match.length !== title.length && match.search(smallWords) > -1 && title.charAt(index - 2) !== \":\" && (title.charAt(index + match.length) !== \"-\" || title.charAt(index - 1) === \"-\") && title.charAt(index - 1).search(/[^\\s-]/) < 0) {\n      return match.toLowerCase();\n    }\n    if (match.substr(1).search(/[A-Z]|\\../) > -1) {\n      return match;\n    }\n    return match.charAt(0).toUpperCase() + match.substr(1);\n  });\n}\n\n// See if s could be an email address. We don't want to send personal data like email.\n// https://support.google.com/analytics/answer/2795983?hl=en\nfunction mightBeEmail(s) {\n  // There's no point trying to validate rfc822 fully, just look for ...@...\n  return typeof s === \"string\" && s.indexOf(\"@\") !== -1;\n}\nvar redacted = \"REDACTED (Potential Email Address)\";\nfunction redactEmail(string) {\n  if (mightBeEmail(string)) {\n    console.warn(\"This arg looks like an email address, redacting.\");\n    return redacted;\n  }\n  return string;\n}\nfunction format() {\n  var s = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"\";\n  var titleCase = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  var redactingEmail = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  var _str = s || \"\";\n  if (titleCase) {\n    _str = toTitleCase(s);\n  }\n  if (redactingEmail) {\n    _str = redactEmail(_str);\n  }\n  return _str;\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = exports.GA4 = void 0;\nvar _gtag = _interopRequireDefault(require(\"./gtag\"));\nvar _format = _interopRequireDefault(require(\"./format\"));\nvar _excluded = [\"eventCategory\", \"eventAction\", \"eventLabel\", \"eventValue\", \"hitType\"],\n  _excluded2 = [\"title\", \"location\"],\n  _excluded3 = [\"page\", \"hitType\"];\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i[\"return\"] && (_r = _i[\"return\"](), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/*\nLinks\nhttps://developers.google.com/gtagjs/reference/api\nhttps://developers.google.com/tag-platform/gtagjs/reference\n*/\n/**\n * @typedef GaOptions\n * @type {Object}\n * @property {boolean} [cookieUpdate=true]\n * @property {number} [cookieExpires=63072000] Default two years\n * @property {string} [cookieDomain=\"auto\"]\n * @property {string} [cookieFlags]\n * @property {string} [userId]\n * @property {string} [clientId]\n * @property {boolean} [anonymizeIp]\n * @property {string} [contentGroup1]\n * @property {string} [contentGroup2]\n * @property {string} [contentGroup3]\n * @property {string} [contentGroup4]\n * @property {string} [contentGroup5]\n * @property {boolean} [allowAdFeatures=true]\n * @property {boolean} [allowAdPersonalizationSignals]\n * @property {boolean} [nonInteraction]\n * @property {string} [page]\n */\n/**\n * @typedef UaEventOptions\n * @type {Object}\n * @property {string} action\n * @property {string} category\n * @property {string} [label]\n * @property {number} [value]\n * @property {boolean} [nonInteraction]\n * @property {('beacon'|'xhr'|'image')} [transport]\n */\n/**\n * @typedef InitOptions\n * @type {Object}\n * @property {string} trackingId\n * @property {GaOptions|any} [gaOptions]\n * @property {Object} [gtagOptions] New parameter\n */\nvar GA4 = /*#__PURE__*/function () {\n  function GA4() {\n    var _this = this;\n    _classCallCheck(this, GA4);\n    _defineProperty(this, \"reset\", function () {\n      _this.isInitialized = false;\n      _this._testMode = false;\n      _this._currentMeasurementId;\n      _this._hasLoadedGA = false;\n      _this._isQueuing = false;\n      _this._queueGtag = [];\n    });\n    _defineProperty(this, \"_gtag\", function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      if (!_this._testMode) {\n        if (_this._isQueuing) {\n          _this._queueGtag.push(args);\n        } else {\n          _gtag[\"default\"].apply(void 0, args);\n        }\n      } else {\n        _this._queueGtag.push(args);\n      }\n    });\n    _defineProperty(this, \"_loadGA\", function (GA_MEASUREMENT_ID, nonce) {\n      var gtagUrl = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : \"https://www.googletagmanager.com/gtag/js\";\n      if (typeof window === \"undefined\" || typeof document === \"undefined\") {\n        return;\n      }\n      if (!_this._hasLoadedGA) {\n        // Global Site Tag (gtag.js) - Google Analytics\n        var script = document.createElement(\"script\");\n        script.async = true;\n        script.src = \"\".concat(gtagUrl, \"?id=\").concat(GA_MEASUREMENT_ID);\n        if (nonce) {\n          script.setAttribute(\"nonce\", nonce);\n        }\n        document.body.appendChild(script);\n        window.dataLayer = window.dataLayer || [];\n        window.gtag = function gtag() {\n          window.dataLayer.push(arguments);\n        };\n        _this._hasLoadedGA = true;\n      }\n    });\n    _defineProperty(this, \"_toGtagOptions\", function (gaOptions) {\n      if (!gaOptions) {\n        return;\n      }\n      var mapFields = {\n        // Old https://developers.google.com/analytics/devguides/collection/analyticsjs/field-reference#cookieUpdate\n        // New https://developers.google.com/analytics/devguides/collection/gtagjs/cookies-user-id#cookie_update\n        cookieUpdate: \"cookie_update\",\n        cookieExpires: \"cookie_expires\",\n        cookieDomain: \"cookie_domain\",\n        cookieFlags: \"cookie_flags\",\n        // must be in set method?\n        userId: \"user_id\",\n        clientId: \"client_id\",\n        anonymizeIp: \"anonymize_ip\",\n        // https://support.google.com/analytics/answer/2853546?hl=en#zippy=%2Cin-this-article\n        contentGroup1: \"content_group1\",\n        contentGroup2: \"content_group2\",\n        contentGroup3: \"content_group3\",\n        contentGroup4: \"content_group4\",\n        contentGroup5: \"content_group5\",\n        // https://support.google.com/analytics/answer/9050852?hl=en\n        allowAdFeatures: \"allow_google_signals\",\n        allowAdPersonalizationSignals: \"allow_ad_personalization_signals\",\n        nonInteraction: \"non_interaction\",\n        page: \"page_path\",\n        hitCallback: \"event_callback\"\n      };\n      var gtagOptions = Object.entries(gaOptions).reduce(function (prev, _ref) {\n        var _ref2 = _slicedToArray(_ref, 2),\n          key = _ref2[0],\n          value = _ref2[1];\n        if (mapFields[key]) {\n          prev[mapFields[key]] = value;\n        } else {\n          prev[key] = value;\n        }\n        return prev;\n      }, {});\n      return gtagOptions;\n    });\n    _defineProperty(this, \"initialize\", function (GA_MEASUREMENT_ID) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      if (!GA_MEASUREMENT_ID) {\n        throw new Error(\"Require GA_MEASUREMENT_ID\");\n      }\n      var initConfigs = typeof GA_MEASUREMENT_ID === \"string\" ? [{\n        trackingId: GA_MEASUREMENT_ID\n      }] : GA_MEASUREMENT_ID;\n      _this._currentMeasurementId = initConfigs[0].trackingId;\n      var gaOptions = options.gaOptions,\n        gtagOptions = options.gtagOptions,\n        nonce = options.nonce,\n        _options$testMode = options.testMode,\n        testMode = _options$testMode === void 0 ? false : _options$testMode,\n        gtagUrl = options.gtagUrl;\n      _this._testMode = testMode;\n      if (!testMode) {\n        _this._loadGA(_this._currentMeasurementId, nonce, gtagUrl);\n      }\n      if (!_this.isInitialized) {\n        _this._gtag(\"js\", new Date());\n        initConfigs.forEach(function (config) {\n          var mergedGtagOptions = _objectSpread(_objectSpread(_objectSpread({}, _this._toGtagOptions(_objectSpread(_objectSpread({}, gaOptions), config.gaOptions))), gtagOptions), config.gtagOptions);\n          if (Object.keys(mergedGtagOptions).length) {\n            _this._gtag(\"config\", config.trackingId, mergedGtagOptions);\n          } else {\n            _this._gtag(\"config\", config.trackingId);\n          }\n        });\n      }\n      _this.isInitialized = true;\n      if (!testMode) {\n        var queues = _toConsumableArray(_this._queueGtag);\n        _this._queueGtag = [];\n        _this._isQueuing = false;\n        while (queues.length) {\n          var queue = queues.shift();\n          _this._gtag.apply(_this, _toConsumableArray(queue));\n          if (queue[0] === \"get\") {\n            _this._isQueuing = true;\n          }\n        }\n      }\n    });\n    _defineProperty(this, \"set\", function (fieldsObject) {\n      if (!fieldsObject) {\n        console.warn(\"`fieldsObject` is required in .set()\");\n        return;\n      }\n      if (_typeof(fieldsObject) !== \"object\") {\n        console.warn(\"Expected `fieldsObject` arg to be an Object\");\n        return;\n      }\n      if (Object.keys(fieldsObject).length === 0) {\n        console.warn(\"empty `fieldsObject` given to .set()\");\n      }\n      _this._gaCommand(\"set\", fieldsObject);\n    });\n    _defineProperty(this, \"_gaCommandSendEvent\", function (eventCategory, eventAction, eventLabel, eventValue, fieldsObject) {\n      _this._gtag(\"event\", eventAction, _objectSpread(_objectSpread({\n        event_category: eventCategory,\n        event_label: eventLabel,\n        value: eventValue\n      }, fieldsObject && {\n        non_interaction: fieldsObject.nonInteraction\n      }), _this._toGtagOptions(fieldsObject)));\n    });\n    _defineProperty(this, \"_gaCommandSendEventParameters\", function () {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      if (typeof args[0] === \"string\") {\n        _this._gaCommandSendEvent.apply(_this, _toConsumableArray(args.slice(1)));\n      } else {\n        var _args$ = args[0],\n          eventCategory = _args$.eventCategory,\n          eventAction = _args$.eventAction,\n          eventLabel = _args$.eventLabel,\n          eventValue = _args$.eventValue,\n          hitType = _args$.hitType,\n          rest = _objectWithoutProperties(_args$, _excluded);\n        _this._gaCommandSendEvent(eventCategory, eventAction, eventLabel, eventValue, rest);\n      }\n    });\n    _defineProperty(this, \"_gaCommandSendTiming\", function (timingCategory, timingVar, timingValue, timingLabel) {\n      _this._gtag(\"event\", \"timing_complete\", {\n        name: timingVar,\n        value: timingValue,\n        event_category: timingCategory,\n        event_label: timingLabel\n      });\n    });\n    _defineProperty(this, \"_gaCommandSendPageview\", function (page, fieldsObject) {\n      if (fieldsObject && Object.keys(fieldsObject).length) {\n        var _this$_toGtagOptions = _this._toGtagOptions(fieldsObject),\n          title = _this$_toGtagOptions.title,\n          location = _this$_toGtagOptions.location,\n          rest = _objectWithoutProperties(_this$_toGtagOptions, _excluded2);\n        _this._gtag(\"event\", \"page_view\", _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, page && {\n          page_path: page\n        }), title && {\n          page_title: title\n        }), location && {\n          page_location: location\n        }), rest));\n      } else if (page) {\n        _this._gtag(\"event\", \"page_view\", {\n          page_path: page\n        });\n      } else {\n        _this._gtag(\"event\", \"page_view\");\n      }\n    });\n    _defineProperty(this, \"_gaCommandSendPageviewParameters\", function () {\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      if (typeof args[0] === \"string\") {\n        _this._gaCommandSendPageview.apply(_this, _toConsumableArray(args.slice(1)));\n      } else {\n        var _args$2 = args[0],\n          page = _args$2.page,\n          hitType = _args$2.hitType,\n          rest = _objectWithoutProperties(_args$2, _excluded3);\n        _this._gaCommandSendPageview(page, rest);\n      }\n    });\n    _defineProperty(this, \"_gaCommandSend\", function () {\n      for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n        args[_key4] = arguments[_key4];\n      }\n      var hitType = typeof args[0] === \"string\" ? args[0] : args[0].hitType;\n      switch (hitType) {\n        case \"event\":\n          _this._gaCommandSendEventParameters.apply(_this, args);\n          break;\n        case \"pageview\":\n          _this._gaCommandSendPageviewParameters.apply(_this, args);\n          break;\n        case \"timing\":\n          _this._gaCommandSendTiming.apply(_this, _toConsumableArray(args.slice(1)));\n          break;\n        case \"screenview\":\n        case \"transaction\":\n        case \"item\":\n        case \"social\":\n        case \"exception\":\n          console.warn(\"Unsupported send command: \".concat(hitType));\n          break;\n        default:\n          console.warn(\"Send command doesn't exist: \".concat(hitType));\n      }\n    });\n    _defineProperty(this, \"_gaCommandSet\", function () {\n      for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n        args[_key5] = arguments[_key5];\n      }\n      if (typeof args[0] === \"string\") {\n        args[0] = _defineProperty({}, args[0], args[1]);\n      }\n      _this._gtag(\"set\", _this._toGtagOptions(args[0]));\n    });\n    _defineProperty(this, \"_gaCommand\", function (command) {\n      for (var _len6 = arguments.length, args = new Array(_len6 > 1 ? _len6 - 1 : 0), _key6 = 1; _key6 < _len6; _key6++) {\n        args[_key6 - 1] = arguments[_key6];\n      }\n      switch (command) {\n        case \"send\":\n          _this._gaCommandSend.apply(_this, args);\n          break;\n        case \"set\":\n          _this._gaCommandSet.apply(_this, args);\n          break;\n        default:\n          console.warn(\"Command doesn't exist: \".concat(command));\n      }\n    });\n    _defineProperty(this, \"ga\", function () {\n      for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n        args[_key7] = arguments[_key7];\n      }\n      if (typeof args[0] === \"string\") {\n        _this._gaCommand.apply(_this, args);\n      } else {\n        var readyCallback = args[0];\n        _this._gtag(\"get\", _this._currentMeasurementId, \"client_id\", function (clientId) {\n          _this._isQueuing = false;\n          var queues = _this._queueGtag;\n          readyCallback({\n            get: function get(property) {\n              return property === \"clientId\" ? clientId : property === \"trackingId\" ? _this._currentMeasurementId : property === \"apiVersion\" ? \"1\" : undefined;\n            }\n          });\n          while (queues.length) {\n            var queue = queues.shift();\n            _this._gtag.apply(_this, _toConsumableArray(queue));\n          }\n        });\n        _this._isQueuing = true;\n      }\n      return _this.ga;\n    });\n    _defineProperty(this, \"event\", function (optionsOrName, params) {\n      if (typeof optionsOrName === \"string\") {\n        _this._gtag(\"event\", optionsOrName, _this._toGtagOptions(params));\n      } else {\n        var action = optionsOrName.action,\n          category = optionsOrName.category,\n          label = optionsOrName.label,\n          value = optionsOrName.value,\n          nonInteraction = optionsOrName.nonInteraction,\n          transport = optionsOrName.transport;\n        if (!category || !action) {\n          console.warn(\"args.category AND args.action are required in event()\");\n          return;\n        }\n\n        // Required Fields\n        var fieldObject = {\n          hitType: \"event\",\n          eventCategory: (0, _format[\"default\"])(category),\n          eventAction: (0, _format[\"default\"])(action)\n        };\n\n        // Optional Fields\n        if (label) {\n          fieldObject.eventLabel = (0, _format[\"default\"])(label);\n        }\n        if (typeof value !== \"undefined\") {\n          if (typeof value !== \"number\") {\n            console.warn(\"Expected `args.value` arg to be a Number.\");\n          } else {\n            fieldObject.eventValue = value;\n          }\n        }\n        if (typeof nonInteraction !== \"undefined\") {\n          if (typeof nonInteraction !== \"boolean\") {\n            console.warn(\"`args.nonInteraction` must be a boolean.\");\n          } else {\n            fieldObject.nonInteraction = nonInteraction;\n          }\n        }\n        if (typeof transport !== \"undefined\") {\n          if (typeof transport !== \"string\") {\n            console.warn(\"`args.transport` must be a string.\");\n          } else {\n            if ([\"beacon\", \"xhr\", \"image\"].indexOf(transport) === -1) {\n              console.warn(\"`args.transport` must be either one of these values: `beacon`, `xhr` or `image`\");\n            }\n            fieldObject.transport = transport;\n          }\n        }\n        _this._gaCommand(\"send\", fieldObject);\n      }\n    });\n    _defineProperty(this, \"send\", function (fieldObject) {\n      _this._gaCommand(\"send\", fieldObject);\n    });\n    this.reset();\n  }\n  _createClass(GA4, [{\n    key: \"gtag\",\n    value: function gtag() {\n      this._gtag.apply(this, arguments);\n    }\n  }]);\n  return GA4;\n}();\nexports.GA4 = GA4;\nvar _default = new GA4();\nexports[\"default\"] = _default;", "\"use strict\";\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = exports.ReactGAImplementation = void 0;\nvar _ga = _interopRequireWildcard(require(\"./ga4\"));\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") { return { \"default\": obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj[\"default\"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\nvar ReactGAImplementation = _ga.GA4;\nexports.ReactGAImplementation = ReactGAImplementation;\nvar _default = _ga[\"default\"];\nexports[\"default\"] = _default;"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,SAAS,IAAI;AACrB,QAAI,OAAO,SAASA,QAAO;AACzB,eAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,aAAK,IAAI,IAAI,UAAU,IAAI;AAAA,MAC7B;AACA,UAAI,OAAO,WAAW,aAAa;AACjC,YAAI;AACJ,YAAI,OAAO,OAAO,SAAS,aAAa;AACtC,iBAAO,YAAY,OAAO,aAAa,CAAC;AACxC,iBAAO,OAAO,SAASA,QAAO;AAC5B,mBAAO,UAAU,KAAK,SAAS;AAAA,UACjC;AAAA,QACF;AACA,SAAC,UAAU,QAAQ,KAAK,MAAM,SAAS,IAAI;AAAA,MAC7C;AAAA,IACF;AACA,QAAI,WAAW;AACf,YAAQ,SAAS,IAAI;AAAA;AAAA;;;ACtBrB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,SAAS,IAAI;AACrB,QAAI,aAAa;AACjB,aAAS,YAAY,QAAQ;AAC3B,aAAO,OAAO,SAAS,EAAE,KAAK,EAAE,QAAQ,qCAAqC,SAAU,OAAO,OAAO,OAAO;AAC1G,YAAI,QAAQ,KAAK,QAAQ,MAAM,WAAW,MAAM,UAAU,MAAM,OAAO,UAAU,IAAI,MAAM,MAAM,OAAO,QAAQ,CAAC,MAAM,QAAQ,MAAM,OAAO,QAAQ,MAAM,MAAM,MAAM,OAAO,MAAM,OAAO,QAAQ,CAAC,MAAM,QAAQ,MAAM,OAAO,QAAQ,CAAC,EAAE,OAAO,QAAQ,IAAI,GAAG;AAC7P,iBAAO,MAAM,YAAY;AAAA,QAC3B;AACA,YAAI,MAAM,OAAO,CAAC,EAAE,OAAO,WAAW,IAAI,IAAI;AAC5C,iBAAO;AAAA,QACT;AACA,eAAO,MAAM,OAAO,CAAC,EAAE,YAAY,IAAI,MAAM,OAAO,CAAC;AAAA,MACvD,CAAC;AAAA,IACH;AAIA,aAAS,aAAa,GAAG;AAEvB,aAAO,OAAO,MAAM,YAAY,EAAE,QAAQ,GAAG,MAAM;AAAA,IACrD;AACA,QAAI,WAAW;AACf,aAAS,YAAY,QAAQ;AAC3B,UAAI,aAAa,MAAM,GAAG;AACxB,gBAAQ,KAAK,kDAAkD;AAC/D,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,aAAS,SAAS;AAChB,UAAI,IAAI,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAC5E,UAAI,YAAY,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACpF,UAAI,iBAAiB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACzF,UAAI,OAAO,KAAK;AAChB,UAAI,WAAW;AACb,eAAO,YAAY,CAAC;AAAA,MACtB;AACA,UAAI,gBAAgB;AAClB,eAAO,YAAY,IAAI;AAAA,MACzB;AACA,aAAO;AAAA,IACT;AAAA;AAAA;;;AC7CA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,SAAS,IAAI,QAAQ,MAAM;AACnC,QAAI,QAAQ,uBAAuB,cAAiB;AACpD,QAAI,UAAU,uBAAuB,gBAAmB;AACxD,QAAI,YAAY,CAAC,iBAAiB,eAAe,cAAc,cAAc,SAAS;AAAtF,QACE,aAAa,CAAC,SAAS,UAAU;AADnC,QAEE,aAAa,CAAC,QAAQ,SAAS;AACjC,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,WAAW,IAAI;AAAA,IAAG;AAChG,aAAS,yBAAyB,QAAQ,UAAU;AAAE,UAAI,UAAU,KAAM,QAAO,CAAC;AAAG,UAAI,SAAS,8BAA8B,QAAQ,QAAQ;AAAG,UAAI,KAAK;AAAG,UAAI,OAAO,uBAAuB;AAAE,YAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAAG,aAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAAE,gBAAM,iBAAiB,CAAC;AAAG,cAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAAU,cAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG,EAAG;AAAU,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO;AAAA,IAAQ;AAC3e,aAAS,8BAA8B,QAAQ,UAAU;AAAE,UAAI,UAAU,KAAM,QAAO,CAAC;AAAG,UAAI,SAAS,CAAC;AAAG,UAAI,aAAa,OAAO,KAAK,MAAM;AAAG,UAAI,KAAK;AAAG,WAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAAE,cAAM,WAAW,CAAC;AAAG,YAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAAU,eAAO,GAAG,IAAI,OAAO,GAAG;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AAClT,aAAS,QAAQ,KAAK;AAAE;AAA2B,aAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,MAAK;AAAE,eAAO,OAAOA;AAAA,MAAK,IAAI,SAAUA,MAAK;AAAE,eAAOA,QAAO,cAAc,OAAO,UAAUA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,MAAK,GAAG,QAAQ,GAAG;AAAA,IAAG;AAC/U,aAAS,mBAAmB,KAAK;AAAE,aAAO,mBAAmB,GAAG,KAAK,iBAAiB,GAAG,KAAK,4BAA4B,GAAG,KAAK,mBAAmB;AAAA,IAAG;AACxJ,aAAS,qBAAqB;AAAE,YAAM,IAAI,UAAU,sIAAsI;AAAA,IAAG;AAC7L,aAAS,iBAAiB,MAAM;AAAE,UAAI,OAAO,WAAW,eAAe,KAAK,OAAO,QAAQ,KAAK,QAAQ,KAAK,YAAY,KAAK,KAAM,QAAO,MAAM,KAAK,IAAI;AAAA,IAAG;AAC7J,aAAS,mBAAmB,KAAK;AAAE,UAAI,MAAM,QAAQ,GAAG,EAAG,QAAO,kBAAkB,GAAG;AAAA,IAAG;AAC1F,aAAS,QAAQ,QAAQ,gBAAgB;AAAE,UAAI,OAAO,OAAO,KAAK,MAAM;AAAG,UAAI,OAAO,uBAAuB;AAAE,YAAI,UAAU,OAAO,sBAAsB,MAAM;AAAG,2BAAmB,UAAU,QAAQ,OAAO,SAAU,KAAK;AAAE,iBAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,QAAY,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,OAAO;AAAA,MAAG;AAAE,aAAO;AAAA,IAAM;AACpV,aAAS,cAAc,QAAQ;AAAE,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,YAAI,SAAS,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAAG,YAAI,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAE,EAAE,QAAQ,SAAU,KAAK;AAAE,0BAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,QAAG,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC,IAAI,QAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAAE,iBAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,QAAG,CAAC;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AACzf,aAAS,eAAe,KAAK,GAAG;AAAE,aAAO,gBAAgB,GAAG,KAAK,sBAAsB,KAAK,CAAC,KAAK,4BAA4B,KAAK,CAAC,KAAK,iBAAiB;AAAA,IAAG;AAC7J,aAAS,mBAAmB;AAAE,YAAM,IAAI,UAAU,2IAA2I;AAAA,IAAG;AAChM,aAAS,4BAA4B,GAAG,QAAQ;AAAE,UAAI,CAAC,EAAG;AAAQ,UAAI,OAAO,MAAM,SAAU,QAAO,kBAAkB,GAAG,MAAM;AAAG,UAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAG,UAAI,MAAM,YAAY,EAAE,YAAa,KAAI,EAAE,YAAY;AAAM,UAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAK,CAAC;AAAG,UAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC,EAAG,QAAO,kBAAkB,GAAG,MAAM;AAAA,IAAG;AAC/Z,aAAS,kBAAkB,KAAK,KAAK;AAAE,UAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAAQ,eAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,IAAK,MAAK,CAAC,IAAI,IAAI,CAAC;AAAG,aAAO;AAAA,IAAM;AAClL,aAAS,sBAAsB,KAAK,GAAG;AAAE,UAAI,KAAK,QAAQ,MAAM,OAAO,eAAe,OAAO,UAAU,IAAI,OAAO,QAAQ,KAAK,IAAI,YAAY;AAAG,UAAI,QAAQ,IAAI;AAAE,YAAI,IAAI,IAAI,IAAI,IAAI,OAAO,CAAC,GAAG,KAAK,MAAI,KAAK;AAAI,YAAI;AAAE,cAAI,MAAM,KAAK,GAAG,KAAK,GAAG,GAAG,MAAM,MAAM,GAAG;AAAE,gBAAI,OAAO,EAAE,MAAM,GAAI;AAAQ,iBAAK;AAAA,UAAI,MAAO,QAAO,EAAE,MAAM,KAAK,GAAG,KAAK,EAAE,GAAG,UAAU,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,WAAW,IAAI,KAAK,KAAG;AAAA,QAAE,SAAS,KAAK;AAAE,eAAK,MAAI,KAAK;AAAA,QAAK,UAAE;AAAU,cAAI;AAAE,gBAAI,CAAC,MAAM,QAAQ,GAAG,QAAQ,MAAM,KAAK,GAAG,QAAQ,EAAE,GAAG,OAAO,EAAE,MAAM,IAAK;AAAA,UAAQ,UAAE;AAAU,gBAAI,GAAI,OAAM;AAAA,UAAI;AAAA,QAAE;AAAE,eAAO;AAAA,MAAM;AAAA,IAAE;AACjlB,aAAS,gBAAgB,KAAK;AAAE,UAAI,MAAM,QAAQ,GAAG,EAAG,QAAO;AAAA,IAAK;AACpE,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AACxJ,aAAS,kBAAkB,QAAQ,OAAO;AAAE,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,YAAI,aAAa,MAAM,CAAC;AAAG,mBAAW,aAAa,WAAW,cAAc;AAAO,mBAAW,eAAe;AAAM,YAAI,WAAW,WAAY,YAAW,WAAW;AAAM,eAAO,eAAe,QAAQ,eAAe,WAAW,GAAG,GAAG,UAAU;AAAA,MAAG;AAAA,IAAE;AAC5U,aAAS,aAAa,aAAa,YAAY,aAAa;AAAE,UAAI,WAAY,mBAAkB,YAAY,WAAW,UAAU;AAAG,UAAI,YAAa,mBAAkB,aAAa,WAAW;AAAG,aAAO,eAAe,aAAa,aAAa,EAAE,UAAU,MAAM,CAAC;AAAG,aAAO;AAAA,IAAa;AAC5R,aAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,YAAM,eAAe,GAAG;AAAG,UAAI,OAAO,KAAK;AAAE,eAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,MAAG,OAAO;AAAE,YAAI,GAAG,IAAI;AAAA,MAAO;AAAE,aAAO;AAAA,IAAK;AAC3O,aAAS,eAAe,KAAK;AAAE,UAAI,MAAM,aAAa,KAAK,QAAQ;AAAG,aAAO,QAAQ,GAAG,MAAM,WAAW,MAAM,OAAO,GAAG;AAAA,IAAG;AAC5H,aAAS,aAAa,OAAO,MAAM;AAAE,UAAI,QAAQ,KAAK,MAAM,YAAY,UAAU,KAAM,QAAO;AAAO,UAAI,OAAO,MAAM,OAAO,WAAW;AAAG,UAAI,SAAS,QAAW;AAAE,YAAI,MAAM,KAAK,KAAK,OAAO,QAAQ,SAAS;AAAG,YAAI,QAAQ,GAAG,MAAM,SAAU,QAAO;AAAK,cAAM,IAAI,UAAU,8CAA8C;AAAA,MAAG;AAAE,cAAQ,SAAS,WAAW,SAAS,QAAQ,KAAK;AAAA,IAAG;AA2C5X,QAAI,MAAmB,WAAY;AACjC,eAASC,OAAM;AACb,YAAI,QAAQ;AACZ,wBAAgB,MAAMA,IAAG;AACzB,wBAAgB,MAAM,SAAS,WAAY;AACzC,gBAAM,gBAAgB;AACtB,gBAAM,YAAY;AAClB,gBAAM;AACN,gBAAM,eAAe;AACrB,gBAAM,aAAa;AACnB,gBAAM,aAAa,CAAC;AAAA,QACtB,CAAC;AACD,wBAAgB,MAAM,SAAS,WAAY;AACzC,mBAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,iBAAK,IAAI,IAAI,UAAU,IAAI;AAAA,UAC7B;AACA,cAAI,CAAC,MAAM,WAAW;AACpB,gBAAI,MAAM,YAAY;AACpB,oBAAM,WAAW,KAAK,IAAI;AAAA,YAC5B,OAAO;AACL,oBAAM,SAAS,EAAE,MAAM,QAAQ,IAAI;AAAA,YACrC;AAAA,UACF,OAAO;AACL,kBAAM,WAAW,KAAK,IAAI;AAAA,UAC5B;AAAA,QACF,CAAC;AACD,wBAAgB,MAAM,WAAW,SAAU,mBAAmB,OAAO;AACnE,cAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAClF,cAAI,OAAO,WAAW,eAAe,OAAO,aAAa,aAAa;AACpE;AAAA,UACF;AACA,cAAI,CAAC,MAAM,cAAc;AAEvB,gBAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,mBAAO,QAAQ;AACf,mBAAO,MAAM,GAAG,OAAO,SAAS,MAAM,EAAE,OAAO,iBAAiB;AAChE,gBAAI,OAAO;AACT,qBAAO,aAAa,SAAS,KAAK;AAAA,YACpC;AACA,qBAAS,KAAK,YAAY,MAAM;AAChC,mBAAO,YAAY,OAAO,aAAa,CAAC;AACxC,mBAAO,OAAO,SAAS,OAAO;AAC5B,qBAAO,UAAU,KAAK,SAAS;AAAA,YACjC;AACA,kBAAM,eAAe;AAAA,UACvB;AAAA,QACF,CAAC;AACD,wBAAgB,MAAM,kBAAkB,SAAU,WAAW;AAC3D,cAAI,CAAC,WAAW;AACd;AAAA,UACF;AACA,cAAI,YAAY;AAAA;AAAA;AAAA,YAGd,cAAc;AAAA,YACd,eAAe;AAAA,YACf,cAAc;AAAA,YACd,aAAa;AAAA;AAAA,YAEb,QAAQ;AAAA,YACR,UAAU;AAAA,YACV,aAAa;AAAA;AAAA,YAEb,eAAe;AAAA,YACf,eAAe;AAAA,YACf,eAAe;AAAA,YACf,eAAe;AAAA,YACf,eAAe;AAAA;AAAA,YAEf,iBAAiB;AAAA,YACjB,+BAA+B;AAAA,YAC/B,gBAAgB;AAAA,YAChB,MAAM;AAAA,YACN,aAAa;AAAA,UACf;AACA,cAAI,cAAc,OAAO,QAAQ,SAAS,EAAE,OAAO,SAAU,MAAM,MAAM;AACvE,gBAAI,QAAQ,eAAe,MAAM,CAAC,GAChC,MAAM,MAAM,CAAC,GACb,QAAQ,MAAM,CAAC;AACjB,gBAAI,UAAU,GAAG,GAAG;AAClB,mBAAK,UAAU,GAAG,CAAC,IAAI;AAAA,YACzB,OAAO;AACL,mBAAK,GAAG,IAAI;AAAA,YACd;AACA,mBAAO;AAAA,UACT,GAAG,CAAC,CAAC;AACL,iBAAO;AAAA,QACT,CAAC;AACD,wBAAgB,MAAM,cAAc,SAAU,mBAAmB;AAC/D,cAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,cAAI,CAAC,mBAAmB;AACtB,kBAAM,IAAI,MAAM,2BAA2B;AAAA,UAC7C;AACA,cAAI,cAAc,OAAO,sBAAsB,WAAW,CAAC;AAAA,YACzD,YAAY;AAAA,UACd,CAAC,IAAI;AACL,gBAAM,wBAAwB,YAAY,CAAC,EAAE;AAC7C,cAAI,YAAY,QAAQ,WACtB,cAAc,QAAQ,aACtB,QAAQ,QAAQ,OAChB,oBAAoB,QAAQ,UAC5B,WAAW,sBAAsB,SAAS,QAAQ,mBAClD,UAAU,QAAQ;AACpB,gBAAM,YAAY;AAClB,cAAI,CAAC,UAAU;AACb,kBAAM,QAAQ,MAAM,uBAAuB,OAAO,OAAO;AAAA,UAC3D;AACA,cAAI,CAAC,MAAM,eAAe;AACxB,kBAAM,MAAM,MAAM,oBAAI,KAAK,CAAC;AAC5B,wBAAY,QAAQ,SAAU,QAAQ;AACpC,kBAAI,oBAAoB,cAAc,cAAc,cAAc,CAAC,GAAG,MAAM,eAAe,cAAc,cAAc,CAAC,GAAG,SAAS,GAAG,OAAO,SAAS,CAAC,CAAC,GAAG,WAAW,GAAG,OAAO,WAAW;AAC5L,kBAAI,OAAO,KAAK,iBAAiB,EAAE,QAAQ;AACzC,sBAAM,MAAM,UAAU,OAAO,YAAY,iBAAiB;AAAA,cAC5D,OAAO;AACL,sBAAM,MAAM,UAAU,OAAO,UAAU;AAAA,cACzC;AAAA,YACF,CAAC;AAAA,UACH;AACA,gBAAM,gBAAgB;AACtB,cAAI,CAAC,UAAU;AACb,gBAAI,SAAS,mBAAmB,MAAM,UAAU;AAChD,kBAAM,aAAa,CAAC;AACpB,kBAAM,aAAa;AACnB,mBAAO,OAAO,QAAQ;AACpB,kBAAI,QAAQ,OAAO,MAAM;AACzB,oBAAM,MAAM,MAAM,OAAO,mBAAmB,KAAK,CAAC;AAClD,kBAAI,MAAM,CAAC,MAAM,OAAO;AACtB,sBAAM,aAAa;AAAA,cACrB;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AACD,wBAAgB,MAAM,OAAO,SAAU,cAAc;AACnD,cAAI,CAAC,cAAc;AACjB,oBAAQ,KAAK,sCAAsC;AACnD;AAAA,UACF;AACA,cAAI,QAAQ,YAAY,MAAM,UAAU;AACtC,oBAAQ,KAAK,6CAA6C;AAC1D;AAAA,UACF;AACA,cAAI,OAAO,KAAK,YAAY,EAAE,WAAW,GAAG;AAC1C,oBAAQ,KAAK,sCAAsC;AAAA,UACrD;AACA,gBAAM,WAAW,OAAO,YAAY;AAAA,QACtC,CAAC;AACD,wBAAgB,MAAM,uBAAuB,SAAU,eAAe,aAAa,YAAY,YAAY,cAAc;AACvH,gBAAM,MAAM,SAAS,aAAa,cAAc,cAAc;AAAA,YAC5D,gBAAgB;AAAA,YAChB,aAAa;AAAA,YACb,OAAO;AAAA,UACT,GAAG,gBAAgB;AAAA,YACjB,iBAAiB,aAAa;AAAA,UAChC,CAAC,GAAG,MAAM,eAAe,YAAY,CAAC,CAAC;AAAA,QACzC,CAAC;AACD,wBAAgB,MAAM,iCAAiC,WAAY;AACjE,mBAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,iBAAK,KAAK,IAAI,UAAU,KAAK;AAAA,UAC/B;AACA,cAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAC/B,kBAAM,oBAAoB,MAAM,OAAO,mBAAmB,KAAK,MAAM,CAAC,CAAC,CAAC;AAAA,UAC1E,OAAO;AACL,gBAAI,SAAS,KAAK,CAAC,GACjB,gBAAgB,OAAO,eACvB,cAAc,OAAO,aACrB,aAAa,OAAO,YACpB,aAAa,OAAO,YACpB,UAAU,OAAO,SACjB,OAAO,yBAAyB,QAAQ,SAAS;AACnD,kBAAM,oBAAoB,eAAe,aAAa,YAAY,YAAY,IAAI;AAAA,UACpF;AAAA,QACF,CAAC;AACD,wBAAgB,MAAM,wBAAwB,SAAU,gBAAgB,WAAW,aAAa,aAAa;AAC3G,gBAAM,MAAM,SAAS,mBAAmB;AAAA,YACtC,MAAM;AAAA,YACN,OAAO;AAAA,YACP,gBAAgB;AAAA,YAChB,aAAa;AAAA,UACf,CAAC;AAAA,QACH,CAAC;AACD,wBAAgB,MAAM,0BAA0B,SAAU,MAAM,cAAc;AAC5E,cAAI,gBAAgB,OAAO,KAAK,YAAY,EAAE,QAAQ;AACpD,gBAAI,uBAAuB,MAAM,eAAe,YAAY,GAC1D,QAAQ,qBAAqB,OAC7B,WAAW,qBAAqB,UAChC,OAAO,yBAAyB,sBAAsB,UAAU;AAClE,kBAAM,MAAM,SAAS,aAAa,cAAc,cAAc,cAAc,cAAc,CAAC,GAAG,QAAQ;AAAA,cACpG,WAAW;AAAA,YACb,CAAC,GAAG,SAAS;AAAA,cACX,YAAY;AAAA,YACd,CAAC,GAAG,YAAY;AAAA,cACd,eAAe;AAAA,YACjB,CAAC,GAAG,IAAI,CAAC;AAAA,UACX,WAAW,MAAM;AACf,kBAAM,MAAM,SAAS,aAAa;AAAA,cAChC,WAAW;AAAA,YACb,CAAC;AAAA,UACH,OAAO;AACL,kBAAM,MAAM,SAAS,WAAW;AAAA,UAClC;AAAA,QACF,CAAC;AACD,wBAAgB,MAAM,oCAAoC,WAAY;AACpE,mBAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,iBAAK,KAAK,IAAI,UAAU,KAAK;AAAA,UAC/B;AACA,cAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAC/B,kBAAM,uBAAuB,MAAM,OAAO,mBAAmB,KAAK,MAAM,CAAC,CAAC,CAAC;AAAA,UAC7E,OAAO;AACL,gBAAI,UAAU,KAAK,CAAC,GAClB,OAAO,QAAQ,MACf,UAAU,QAAQ,SAClB,OAAO,yBAAyB,SAAS,UAAU;AACrD,kBAAM,uBAAuB,MAAM,IAAI;AAAA,UACzC;AAAA,QACF,CAAC;AACD,wBAAgB,MAAM,kBAAkB,WAAY;AAClD,mBAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,iBAAK,KAAK,IAAI,UAAU,KAAK;AAAA,UAC/B;AACA,cAAI,UAAU,OAAO,KAAK,CAAC,MAAM,WAAW,KAAK,CAAC,IAAI,KAAK,CAAC,EAAE;AAC9D,kBAAQ,SAAS;AAAA,YACf,KAAK;AACH,oBAAM,8BAA8B,MAAM,OAAO,IAAI;AACrD;AAAA,YACF,KAAK;AACH,oBAAM,iCAAiC,MAAM,OAAO,IAAI;AACxD;AAAA,YACF,KAAK;AACH,oBAAM,qBAAqB,MAAM,OAAO,mBAAmB,KAAK,MAAM,CAAC,CAAC,CAAC;AACzE;AAAA,YACF,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AACH,sBAAQ,KAAK,6BAA6B,OAAO,OAAO,CAAC;AACzD;AAAA,YACF;AACE,sBAAQ,KAAK,+BAA+B,OAAO,OAAO,CAAC;AAAA,UAC/D;AAAA,QACF,CAAC;AACD,wBAAgB,MAAM,iBAAiB,WAAY;AACjD,mBAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,iBAAK,KAAK,IAAI,UAAU,KAAK;AAAA,UAC/B;AACA,cAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAC/B,iBAAK,CAAC,IAAI,gBAAgB,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,UAChD;AACA,gBAAM,MAAM,OAAO,MAAM,eAAe,KAAK,CAAC,CAAC,CAAC;AAAA,QAClD,CAAC;AACD,wBAAgB,MAAM,cAAc,SAAU,SAAS;AACrD,mBAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjH,iBAAK,QAAQ,CAAC,IAAI,UAAU,KAAK;AAAA,UACnC;AACA,kBAAQ,SAAS;AAAA,YACf,KAAK;AACH,oBAAM,eAAe,MAAM,OAAO,IAAI;AACtC;AAAA,YACF,KAAK;AACH,oBAAM,cAAc,MAAM,OAAO,IAAI;AACrC;AAAA,YACF;AACE,sBAAQ,KAAK,0BAA0B,OAAO,OAAO,CAAC;AAAA,UAC1D;AAAA,QACF,CAAC;AACD,wBAAgB,MAAM,MAAM,WAAY;AACtC,mBAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,iBAAK,KAAK,IAAI,UAAU,KAAK;AAAA,UAC/B;AACA,cAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAC/B,kBAAM,WAAW,MAAM,OAAO,IAAI;AAAA,UACpC,OAAO;AACL,gBAAI,gBAAgB,KAAK,CAAC;AAC1B,kBAAM,MAAM,OAAO,MAAM,uBAAuB,aAAa,SAAU,UAAU;AAC/E,oBAAM,aAAa;AACnB,kBAAI,SAAS,MAAM;AACnB,4BAAc;AAAA,gBACZ,KAAK,SAAS,IAAI,UAAU;AAC1B,yBAAO,aAAa,aAAa,WAAW,aAAa,eAAe,MAAM,wBAAwB,aAAa,eAAe,MAAM;AAAA,gBAC1I;AAAA,cACF,CAAC;AACD,qBAAO,OAAO,QAAQ;AACpB,oBAAI,QAAQ,OAAO,MAAM;AACzB,sBAAM,MAAM,MAAM,OAAO,mBAAmB,KAAK,CAAC;AAAA,cACpD;AAAA,YACF,CAAC;AACD,kBAAM,aAAa;AAAA,UACrB;AACA,iBAAO,MAAM;AAAA,QACf,CAAC;AACD,wBAAgB,MAAM,SAAS,SAAU,eAAe,QAAQ;AAC9D,cAAI,OAAO,kBAAkB,UAAU;AACrC,kBAAM,MAAM,SAAS,eAAe,MAAM,eAAe,MAAM,CAAC;AAAA,UAClE,OAAO;AACL,gBAAI,SAAS,cAAc,QACzB,WAAW,cAAc,UACzB,QAAQ,cAAc,OACtB,QAAQ,cAAc,OACtB,iBAAiB,cAAc,gBAC/B,YAAY,cAAc;AAC5B,gBAAI,CAAC,YAAY,CAAC,QAAQ;AACxB,sBAAQ,KAAK,uDAAuD;AACpE;AAAA,YACF;AAGA,gBAAI,cAAc;AAAA,cAChB,SAAS;AAAA,cACT,gBAAgB,GAAG,QAAQ,SAAS,GAAG,QAAQ;AAAA,cAC/C,cAAc,GAAG,QAAQ,SAAS,GAAG,MAAM;AAAA,YAC7C;AAGA,gBAAI,OAAO;AACT,0BAAY,cAAc,GAAG,QAAQ,SAAS,GAAG,KAAK;AAAA,YACxD;AACA,gBAAI,OAAO,UAAU,aAAa;AAChC,kBAAI,OAAO,UAAU,UAAU;AAC7B,wBAAQ,KAAK,2CAA2C;AAAA,cAC1D,OAAO;AACL,4BAAY,aAAa;AAAA,cAC3B;AAAA,YACF;AACA,gBAAI,OAAO,mBAAmB,aAAa;AACzC,kBAAI,OAAO,mBAAmB,WAAW;AACvC,wBAAQ,KAAK,0CAA0C;AAAA,cACzD,OAAO;AACL,4BAAY,iBAAiB;AAAA,cAC/B;AAAA,YACF;AACA,gBAAI,OAAO,cAAc,aAAa;AACpC,kBAAI,OAAO,cAAc,UAAU;AACjC,wBAAQ,KAAK,oCAAoC;AAAA,cACnD,OAAO;AACL,oBAAI,CAAC,UAAU,OAAO,OAAO,EAAE,QAAQ,SAAS,MAAM,IAAI;AACxD,0BAAQ,KAAK,iFAAiF;AAAA,gBAChG;AACA,4BAAY,YAAY;AAAA,cAC1B;AAAA,YACF;AACA,kBAAM,WAAW,QAAQ,WAAW;AAAA,UACtC;AAAA,QACF,CAAC;AACD,wBAAgB,MAAM,QAAQ,SAAU,aAAa;AACnD,gBAAM,WAAW,QAAQ,WAAW;AAAA,QACtC,CAAC;AACD,aAAK,MAAM;AAAA,MACb;AACA,mBAAaA,MAAK,CAAC;AAAA,QACjB,KAAK;AAAA,QACL,OAAO,SAAS,OAAO;AACrB,eAAK,MAAM,MAAM,MAAM,SAAS;AAAA,QAClC;AAAA,MACF,CAAC,CAAC;AACF,aAAOA;AAAA,IACT,EAAE;AACF,YAAQ,MAAM;AACd,QAAI,WAAW,IAAI,IAAI;AACvB,YAAQ,SAAS,IAAI;AAAA;AAAA;;;ACjbrB;AAAA;AAEA,aAAS,QAAQ,KAAK;AAAE;AAA2B,aAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,MAAK;AAAE,eAAO,OAAOA;AAAA,MAAK,IAAI,SAAUA,MAAK;AAAE,eAAOA,QAAO,cAAc,OAAO,UAAUA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,MAAK,GAAG,QAAQ,GAAG;AAAA,IAAG;AAC/U,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,SAAS,IAAI,QAAQ,wBAAwB;AACrD,QAAI,MAAM,wBAAwB,aAAgB;AAClD,aAAS,yBAAyB,aAAa;AAAE,UAAI,OAAO,YAAY,WAAY,QAAO;AAAM,UAAI,oBAAoB,oBAAI,QAAQ;AAAG,UAAI,mBAAmB,oBAAI,QAAQ;AAAG,cAAQ,2BAA2B,SAASC,0BAAyBC,cAAa;AAAE,eAAOA,eAAc,mBAAmB;AAAA,MAAmB,GAAG,WAAW;AAAA,IAAG;AAC9U,aAAS,wBAAwB,KAAK,aAAa;AAAE,UAAI,CAAC,eAAe,OAAO,IAAI,YAAY;AAAE,eAAO;AAAA,MAAK;AAAE,UAAI,QAAQ,QAAQ,QAAQ,GAAG,MAAM,YAAY,OAAO,QAAQ,YAAY;AAAE,eAAO,EAAE,WAAW,IAAI;AAAA,MAAG;AAAE,UAAI,QAAQ,yBAAyB,WAAW;AAAG,UAAI,SAAS,MAAM,IAAI,GAAG,GAAG;AAAE,eAAO,MAAM,IAAI,GAAG;AAAA,MAAG;AAAE,UAAI,SAAS,CAAC;AAAG,UAAI,wBAAwB,OAAO,kBAAkB,OAAO;AAA0B,eAAS,OAAO,KAAK;AAAE,YAAI,QAAQ,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG,GAAG;AAAE,cAAI,OAAO,wBAAwB,OAAO,yBAAyB,KAAK,GAAG,IAAI;AAAM,cAAI,SAAS,KAAK,OAAO,KAAK,MAAM;AAAE,mBAAO,eAAe,QAAQ,KAAK,IAAI;AAAA,UAAG,OAAO;AAAE,mBAAO,GAAG,IAAI,IAAI,GAAG;AAAA,UAAG;AAAA,QAAE;AAAA,MAAE;AAAE,aAAO,SAAS,IAAI;AAAK,UAAI,OAAO;AAAE,cAAM,IAAI,KAAK,MAAM;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AAC1yB,QAAI,wBAAwB,IAAI;AAChC,YAAQ,wBAAwB;AAChC,QAAI,WAAW,IAAI,SAAS;AAC5B,YAAQ,SAAS,IAAI;AAAA;AAAA;", "names": ["gtag", "obj", "GA4", "obj", "_getRequireWildcardCache", "nodeInterop"]}