v1.2.0 - Mon Jun 22 2020 15:36:00 PST
--------------------------------------

- Added support for optional attributes that can be added to the generated script tag [(#52)](https://github.com/dozoisch/react-async-script/pull/52)
- Update various dependencies (notably babel/react) [(#48)](https://github.com/dozoisch/react-async-script/pull/48)


v1.1.1 - Sun Jul 14 2019 11:14:00 PST
--------------------------------------

- Add node 12 to build [(#46)](https://github.com/dozoisch/react-async-script/pull/46)



v1.1.0 - Sun Jul 14 2019 10:19:00 PST
--------------------------------------

- Switch to Jest [(#45)](https://github.com/dozoisch/react-async-script/pull/45)
- Upgrade dependencies and add es module [(#44)](https://github.com/dozoisch/react-async-script/pull/44)



v1.0.1 - Sun 17 Apr 2019 19:14:00 PST
--------------------------------------

- Support for adding a script id attribute [(#43)](https://github.com/dozoisch/react-async-script/pull/43)



v1.0.0 - Fri 17 Aug 2018 17:32:00 PST
--------------------------------------

- React forward ref [(#37)](https://github.com/dozoisch/react-async-script/pull/37)
- Update to react 16.4.1 [(#37)](https://github.com/dozoisch/react-async-script/pull/37)
- Hoist non react statics [(#35)](https://github.com/dozoisch/react-async-script/pull/35)
- Updated Travis Node versions [(#36)](https://github.com/dozoisch/react-async-script/pull/36)
- Refactor to new HOC pattern [(#34)](https://github.com/dozoisch/react-async-script/pull/34)
- Remove old broken IE support [(#34)](https://github.com/dozoisch/react-async-script/pull/34)
- Add migration notes [(#40)](https://github.com/dozoisch/react-async-script/pull/40)



v0.11.1 - Sat, 4 Aug 2018 12:46:00 PST
--------------------------------------

- Remove babel-runtime peer dep [(#32)](https://github.com/dozoisch/react-async-script/pull/32)
- Readme updates [(#31)](https://github.com/dozoisch/react-async-script/pull/31)



v0.11.0 - Sun, 29 Jul 2018 11:58:00 PST
--------------------------------------

- Remove transform runtime [(#29)](https://github.com/dozoisch/react-async-script/pull/29)
- Added dynamic url capability [(#30)](https://github.com/dozoisch/react-async-script/pull/30)



v0.10.0 - Tue, 24 Jul 2018 14:40:00 PST
--------------------------------------

- Clean up use of Map to remove core-js polyfills [(#27)](https://github.com/dozoisch/react-async-script/pull/27)



v0.9.1 - Wed, 19 Apr 2017 6:05:00 PST
--------------------------------------

- Fixed issue where method was not bound properly [(#19)](https://github.com/dozoisch/react-async-script/pull/19)



v0.9.0 - Sun, 16 Apr 2017 4:12:00 PST
--------------------------------------

- Changed updated to react >=15.5 [(#18)](https://github.com/dozoisch/react-async-script/pull/18)



v0.8.0 - Thu, 23 Mar 2017 5:46:00 PST
--------------------------------------

- Added removeOnUnmount parameter [(#14)](https://github.com/dozoisch/react-async-script/pull/14)



v0.7.0 - Sat, 04 Mar 2017 6:37:00 PST
--------------------------------------

- Updated deps to react 15 and babel 6 [(#13)](https://github.com/dozoisch/react-async-script/pull/13)
- Go back to manual changelogs



v0.6.0 - Thu, 02 Jun 2016 01:18:21 GMT
--------------------------------------

- [0bc67b2](../../commit/0bc67b2) [fixed] typo in documentation [(#9)](https://github.com/dozoisch/react-async-script/pull/9)



v0.5.1 - Fri, 15 Jan 2016 13:21:01 GMT
--------------------------------------

- [65d1313](../../commit/65d1313) [added] old history to new changelog file



v0.5.0 - Thu, 15 Oct 2015 21:10:33 GMT
--------------------------------------

- [9059f9e](../../commit/9059f9e) [changed] travis to build only master & prs
- [52bb6d5](../../commit/52bb6d5) [changed] updated deps
- [4920f59](../../commit/4920f59) [changed] Build Tools to use babel
- [253dbf2](../../commit/253dbf2) [fixed] onLoad propname in readme

## 0.4.0

- Fixed issue with refs
- Bump all deps

## 0.3.2
- Bump deps

## 0.3.1
- Removed uncessary "use strict"
- Bump deps

## 0.3.0
- Added a way to expose child functions

## 0.2.2
- Fix instanciation when global object already exists

## 0.2.1
- Put runtime in deps and not dev-deps

## 0.2.0
- Back to es6 Map

## 0.1.2
- Added a small test
- Added some badges
- small fixes

## 0.1.1
- reverted es6 map back to plain object

## 0.1.0
- Added the composition function
- Added build files
- Added david-dm badge
- Initial readme
