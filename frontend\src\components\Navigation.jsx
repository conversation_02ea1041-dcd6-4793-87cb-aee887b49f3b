import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { History, Wand2, FileText, Info, HelpCircle, BookOpen, Menu, X } from 'lucide-react';

import Logo from './Logo';

const Navigation = () => {
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const navItems = [
    {
      name: 'Generate',
      path: '/generate',
      icon: Wand2,
    },
    {
      name: 'History',
      path: '/history',
      icon: History,
    },
    {
      name: 'Blog',
      path: '/blog',
      icon: BookOpen,
    },
    {
      name: 'About',
      path: '/about',
      icon: Info,
    },
    {
      name: 'FAQ',
      path: '/faq',
      icon: HelpCircle,
    },
    {
      name: 'Terms',
      path: '/terms',
      icon: FileText,
    },
  ];

  const isActive = (path) => {
    return location.pathname === path;
  };



  return (
    <>
    <header className="sticky top-0 z-40 bg-gray-900/80 backdrop-blur-sm border-b border-gray-700 animate-slide-down">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3 group">
            <Logo size="medium" className="transition-transform duration-300 group-hover:scale-105" />

          </Link>

          {/* Navigation Links */}
          <nav className="hidden md:flex items-center space-x-1">
            {navItems.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.path}
                  to={item.path}

                  className={`
                    flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium
                    transition-all duration-200 hover:scale-105
                    ${isActive(item.path)
                      ? 'bg-purple-900/30 text-purple-300 animate-scale-in'
                      : 'text-gray-300 hover:text-white hover:bg-gray-800'
                    }
                  `}
                >
                  <Icon className="w-4 h-4" />
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* Mobile Hamburger Menu Button */}
          <div className="md:hidden">
            <button
              onClick={toggleMobileMenu}
              className="flex items-center justify-center w-10 h-10 rounded-lg text-gray-300 hover:text-white hover:bg-gray-800 transition-colors duration-200"
              aria-label="Toggle mobile menu"
            >
              {isMobileMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>


        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden border-t border-gray-700 bg-gray-800 shadow-lg">
          <div className="px-4 pt-2 pb-3 space-y-1">
            {navItems.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.path}
                  to={item.path}
                  onClick={closeMobileMenu}
                  className={`
                    flex items-center space-x-3 px-3 py-3 rounded-lg text-base font-medium
                    transition-all duration-200
                    ${isActive(item.path)
                      ? 'bg-purple-900/30 text-purple-300 border-l-4 border-purple-400'
                      : 'text-gray-300 hover:text-white hover:bg-gray-700'
                    }
                  `}
                >
                  <Icon className="w-5 h-5" />
                  <span>{item.name}</span>
                </Link>
              );
            })}
          </div>
        </div>
      )}
    </header>

    {/* Mobile Menu Overlay */}
    {isMobileMenuOpen && (
      <div
        className="fixed inset-0 bg-black bg-opacity-25 z-30 md:hidden"
        onClick={closeMobileMenu}
      />
    )}
    </>
  );
};

export default Navigation;
